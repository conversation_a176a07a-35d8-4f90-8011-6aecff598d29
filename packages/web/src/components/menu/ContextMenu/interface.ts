export type TriggerEvent =  'click' | 'context-menu';
/**
 * `ContextMenu` 组件的 Props
 */
export interface ContextMenuProps {
  /**
   * 选择（点击）任意菜单项后，是否自动关闭菜单。
   * - 默认：`true`
   * - 设为 `false` 可用于需要在一次打开中进行多次交互的场景。
   */
  hideOnSelect?: boolean;
  /**
   * 触发菜单方式
   * - 默认：context-menu
   * - 可选 click: 点击 | context-menu: 右键菜单
   */
  trigger?: TriggerEvent | TriggerEvent[];
}

/**
 * `ContextMenu` 对外触发的事件
 */
export interface ContextMenuEmits {
  /**
   * 当某个菜单项被选中时触发（包含子菜单项）。
   * @param e 事件名，固定为 `'select'`
   * @param value 该菜单项的值（来自 `ContextMenuItemProps.value`）
   * @param event 触发该选择的原生事件对象
   */
  (e: 'select', value: any, event: Event): void;
}

/**
 * `ContextMenuItem` 菜单项 Props
 */
export interface ContextMenuItemProps {
  /** 菜单项显示文案 */
  label?: string;
  /** 菜单项值，将在 `select` 事件中原样透传 */
  value?: string | number;
  /** 是否禁用该项，禁用后不响应点击，默认 `false` */
  disabled?: boolean;
  /** 是否展示危险态样式（如红色），默认 `false` */
  danger?: boolean;
  /**
   * 图标类名或标识（与样式体系配合使用）。
   * 将作为类名挂载在图标容器上。
   */
  icon?: string;
}

/**
 * `ContextMenuItem` 对外触发的事件
 */
export interface ContextMenuItemEmits {
  /**
   * 当该菜单项被点击时触发。
   * 通常同时会通过父级的 `select` 事件对外通知。
   * @param e 事件名，固定为 `'click'`
   * @param value 该菜单项的值
   * @param event 原生事件对象
   */
  (e: 'click', value: string | number, event: Event): void;
}

/**
 * `ContextMenuGroup` 分组容器 Props
 */
export interface ContextMenuGroupProps {
  /** 分组标题，可选，存在时显示在组首 */
  title?: string;
}

/**
 * `ContextMenuSubmenu` 子菜单 Props
 */
export interface ContextMenuSubmenuProps {
  /** 子菜单触发器显示文案 */
  label: string;
  /** 是否禁用子菜单触发，默认 `false` */
  disabled?: boolean;
  /** 子菜单触发器图标类名或标识 */
  icon?: string;
}

/**
 * `ContextMenuSubmenu` 对外触发的事件
 */
export interface ContextMenuSubmenuEmits {
  /**
   * 当子菜单内部的某个菜单项被选中时触发。
   * 事件会向上冒泡到 `ContextMenu`。
   * @param e 事件名，固定为 `'select'`
   * @param value 子菜单项值
   * @param event 原生事件对象
   */
  (e: 'select', value: any, event: Event): void;
}
