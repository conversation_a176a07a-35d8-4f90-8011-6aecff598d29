@import "../../../../assets/less/variables.less";
@contextMenuPrefix: @{u-prefix}-context-menu;

// 右键菜单容器样式
.@{contextMenuPrefix}-trigger {
  display: contents;
}

// 遮罩层
.@{contextMenuPrefix}-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

// 菜单容器
.@{contextMenuPrefix}-menu {
  position: fixed;
  z-index: 1000;
  min-width: 100px;
  padding: 4px 0;
  width: max-content;
  background: var(--u-bg-color-3);
  border: 1px solid var(--gray-3);
  border-radius: var(--u-radius-default);
  box-shadow: var(--u-shadow-lg);
  user-select: none;

  // 小尺寸菜单
  &-small {
    min-width: 78px;
    padding: 0px 0;
  }
}

// 菜单出现过滤动画
.@{contextMenuPrefix}-menu-transition-enter-from,
.@{contextMenuPrefix}-menu-transition-leave-to {
  opacity: 0;
  transform: scaleY(0.86);
}

.@{contextMenuPrefix}-menu-transition-enter-active,
.@{contextMenuPrefix}-menu-transition-leave-active {
  transition: opacity 200ms ease, transform 160ms ease;
  transform-origin: top;
}

// 子菜单弹出层
.@{contextMenuPrefix}-submenu-popup {
  position: absolute;
  left: 100%;
  top: 0;
  z-index: 1001;
}

// 菜单项样式
.@{contextMenuPrefix}-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  margin: 0 4px;
  font-size: 14px;
  line-height: 22px;
  color: var(--u-text-color);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
  white-space: nowrap;

  // 小尺寸菜单项
  .@{contextMenuPrefix}-menu-small & {
    padding: 2px 6px;
    margin: 0 0px;
    font-size: 12px;
    line-height: 16px;
  }

  &:hover {
    background-color: var(--u-bg-color-3-hover);
  }

  &:active {
    background-color: var(--u-bg-color-3-active);
  }

  &-disabled {
    color: var(--u-text-color-disabled);
    cursor: not-allowed;
    &:hover {
      background-color: transparent;
    }
  }

  &-danger {
    color: var(--u-text-color-danger);
    &:hover {
      background-color: rgba(var(--red-5), 0.1);
    }
  }

  &-icon {
    margin-right: 8px;
    font-size: 14px;

    // 小尺寸图标
    .@{contextMenuPrefix}-menu-small & {
      margin-right: 4px;
      font-size: 10px;
    }
  }

  &-label {
    flex: 1;
  }
}

// 子菜单样式
.@{contextMenuPrefix}-submenu {
  position: relative; // 为子菜单提供定位上下文
  &-arrow {
    margin-left: 8px;
    font-size: 10px;
    color: rgb(var(--gray-4));
    transition: color 90ms linear;

    // 小尺寸子菜单箭头
    .@{contextMenuPrefix}-menu-small & {
      margin-left: 4px;
      font-size: 6px;
    }
  }

  &-active {
    background-color: rgba(0, 0, 0, 0.04);
    // 当前菜单项的直接箭头
    >.@{contextMenuPrefix}-submenu-arrow {
      color: rgb(var(--gray-6));
    }
  }

  &-disabled {
    .@{contextMenuPrefix}-submenu-arrow {
      color: rgb(var(--gray-3));
    }
  }
}

// 分组标题样式
.@{contextMenuPrefix}-group {
  &-title {
    padding: 4px 6px 4px;
    font-size: 12px;
    line-height: 20px;
    color: var(--u-text-color-secondary);
    font-weight: 500;

    // 小尺寸分组标题
    .@{contextMenuPrefix}-menu-small & {
      padding: 0px 2px 0px;
      font-size: 10px;
      line-height: 14px;
    }
  }

  &:not(:first-child) {
    border-top: 1px solid var(--u-color-neutral-2);
    margin-top: 2px;
    padding-top: 2px;
  }
}

// 分隔线样式
.@{contextMenuPrefix}-divider {
  height: 1px;
  background-color: var(--u-color-neutral-2);
  margin: 4px 0;
}
