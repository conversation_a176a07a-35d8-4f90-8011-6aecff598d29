import type { JSX } from "vue/jsx-runtime";

export type PageHeaderSize =  'mini' | 'small' | 'default' | 'large';

export interface PageHeaderProps {
  title?: string;
  subtitle?: string;
  size?: PageHeaderSize;
}

export interface PageHeaderSlots {
  /**
   * 标题
   */
  title?: () => JSX.Element;
  /**
   * 子标题
   */
  subtitle?: () => JSX.Element;
  /**
   * 额外
   */
  extra?: () => JSX.Element;
  /**
   * 标题前面
   */
  prefix?: () => JSX.Element;
}


export interface PageHeaderEmits {
  (e: 'back'): void;
}
