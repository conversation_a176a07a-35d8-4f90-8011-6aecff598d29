<script lang="ts">
import { defineComponent, ref } from 'vue';
import {
  ContextMenu,
  ContextMenuItem,
  ContextMenuGroup,
  ContextMenuDivider,
  ContextMenuSubmenu
} from '@xiaou66/u-web-ui';

export default defineComponent({
  name: 'ContextMenuDemo',
  components: {
    ContextMenu,
    ContextMenuItem,
    ContextMenuGroup,
    ContextMenuDivider,
    ContextMenuSubmenu,
  },
  setup() {
    const selectedValue = ref('');

    const handleMenuSelect = (value: string | number) => {
      console.log('选择了菜单项:', value);
      selectedValue.value = `选择了: ${value} (${new Date().toLocaleTimeString()})`;
    };

    return {
      selectedValue,
      handleMenuSelect,
    };
  },
});
</script>
<template>
  <div class="context-menu-demo">
    <h2>右键菜单演示</h2>
    <t-slider class="u-slider u-slider-no-fill u-slider-green" />
    <div class="demo-section">
      <h3>基础用法 1</h3>
      <ContextMenu @select="handleMenuSelect">
        <div class="demo-target">
          右键点击这里
        </div>
        <template #content>
          <ContextMenuItem
            label="复制"
            value="copy"
            icon="📋"
          />
          <ContextMenuItem
            label="粘贴"
            value="paste"
            icon="📄"
          />
          <ContextMenuDivider />
          <ContextMenuItem
            label="删除"
            value="delete"
            icon="🗑️"
            danger
          />
        </template>
      </ContextMenu>
    </div>
    <div class="demo-section">
      <h3>基础用法 2</h3>
      <ContextMenu @select="handleMenuSelect" trigger="click">
        <div class="demo-target">
          左键点击这里
        </div>
        <template #content>
          <ContextMenuItem
            label="复制"
            value="copy"
            icon="📋"
          />
          <ContextMenuItem
            label="粘贴"
            value="paste"
            icon="📄"
          />
          <ContextMenuDivider />
          <ContextMenuItem
            label="删除"
            value="delete"
            icon="🗑️"
            danger
          />
        </template>
      </ContextMenu>
    </div>

    <div class="demo-section">
      <h3>分组菜单</h3>
      <ContextMenu @select="handleMenuSelect">
        <div class="demo-target">
          右键点击查看分组菜单
        </div>
        <template #content>
          <ContextMenuGroup title="编辑">
            <ContextMenuItem label="撤销" value="undo" icon="↩️" />
            <ContextMenuItem label="重做" value="redo" icon="↪️" />
          </ContextMenuGroup>
          <ContextMenuGroup title="文件操作">
            <ContextMenuItem label="新建" value="new" icon="➕" />
            <ContextMenuItem label="打开" value="open" icon="📂" />
            <ContextMenuItem label="保存" value="save" icon="💾" />
          </ContextMenuGroup>
        </template>
      </ContextMenu>
    </div>

    <div class="demo-section">
      <h3>多级菜单</h3>
      <ContextMenu @select="handleMenuSelect">
        <div class="demo-target">
          右键查看多级菜单
        </div>
        <template #content>
          <ContextMenuItem label="刷新" value="refresh" icon="🔄" />
          <ContextMenuDivider />

          <ContextMenuSubmenu label="新建" icon="➕">
            <ContextMenuItem label="文件夹" value="new-folder" icon="📁" />
            <ContextMenuItem label="文本文件" value="new-text" icon="📄" />
            <ContextMenuSubmenu label="代码文件" icon="💻">
              <ContextMenuItem label="JavaScript" value="new-js" icon="📜" />
              <ContextMenuItem label="TypeScript" value="new-ts" icon="📘" />
              <ContextMenuItem label="Vue 组件" value="new-vue" icon="💚" />
              <ContextMenuItem label="CSS 样式" value="new-css" icon="🎨" />
            </ContextMenuSubmenu>
            <ContextMenuDivider />
            <ContextMenuItem label="快捷方式" value="new-shortcut" icon="🔗" />
          </ContextMenuSubmenu>

          <ContextMenuSubmenu label="编辑" icon="✏️">
            <ContextMenuItem label="剪切" value="cut" icon="✂️" />
            <ContextMenuItem label="复制" value="copy-sub" icon="📋" />
            <ContextMenuItem label="粘贴" value="paste-sub" icon="📄" />
            <ContextMenuDivider />
            <ContextMenuSubmenu label="选择" icon="🎯">
              <ContextMenuItem label="全选" value="select-all" icon="🔳" />
              <ContextMenuItem label="反选" value="select-invert" icon="🔄" />
              <ContextMenuItem label="清除选择" value="select-none" icon="❌" />
            </ContextMenuSubmenu>
          </ContextMenuSubmenu>

          <ContextMenuDivider />
          <ContextMenuItem label="属性" value="properties" icon="⚙️" />
        </template>
      </ContextMenu>
    </div>

    <div class="demo-section">
      <h3>禁用项和深层嵌套</h3>
      <ContextMenu @select="handleMenuSelect">
        <div class="demo-target">
          右键查看复杂菜单
        </div>
        <template #content>
          <ContextMenuItem label="可用项" value="enabled" icon="✅" />
          <ContextMenuItem label="禁用项" value="disabled" icon="❌" disabled />

          <ContextMenuSubmenu label="工具" icon="🔧">
            <ContextMenuItem label="搜索" value="search" icon="🔍" />
            <ContextMenuSubmenu label="视图" icon="👁️">
              <ContextMenuItem label="列表视图" value="view-list" icon="📋" />
              <ContextMenuItem label="图标视图" value="view-icons" icon="🎯" />
              <ContextMenuSubmenu label="详细信息" icon="📊">
                <ContextMenuItem label="显示大小" value="show-size" />
                <ContextMenuItem label="显示日期" value="show-date" />
                <ContextMenuItem label="显示类型" value="show-type" />
                <ContextMenuSubmenu label="排序方式" icon="🔄">
                  <ContextMenuItem label="按名称" value="sort-name" />
                  <ContextMenuItem label="按大小" value="sort-size" />
                  <ContextMenuItem label="按日期" value="sort-date" />
                  <ContextMenuItem label="按类型" value="sort-type" />
                </ContextMenuSubmenu>
              </ContextMenuSubmenu>
            </ContextMenuSubmenu>
            <ContextMenuDivider />
            <ContextMenuSubmenu label="设置" icon="⚙️" disabled>
              <ContextMenuItem label="通用设置" value="settings-general" />
              <ContextMenuItem label="高级设置" value="settings-advanced" />
            </ContextMenuSubmenu>
          </ContextMenuSubmenu>
          <ContextMenuSubmenu label="工具" icon="🔧">
            <ContextMenuItem label="搜索" value="search" icon="🔍" />
            <ContextMenuSubmenu label="视图" icon="👁️">
              <ContextMenuItem label="列表视图" value="view-list" icon="📋" />
              <ContextMenuItem label="图标视图" value="view-icons" icon="🎯" />
              <ContextMenuSubmenu label="详细信息" icon="📊">
                <ContextMenuItem label="显示大小" value="show-size" />
                <ContextMenuItem label="显示日期" value="show-date" />
                <ContextMenuItem label="显示类型" value="show-type" />
                <ContextMenuSubmenu label="排序方式" icon="🔄">
                  <ContextMenuItem label="按名称" value="sort-name" />
                  <ContextMenuItem label="按大小" value="sort-size" />
                  <ContextMenuItem label="按日期" value="sort-date" />
                  <ContextMenuItem label="按类型" value="sort-type" />
                </ContextMenuSubmenu>
              </ContextMenuSubmenu>
            </ContextMenuSubmenu>
            <ContextMenuDivider />
            <ContextMenuSubmenu label="设置" icon="⚙️" disabled>
              <ContextMenuItem label="通用设置" value="settings-general" />
              <ContextMenuItem label="高级设置" value="settings-advanced" />
            </ContextMenuSubmenu>
          </ContextMenuSubmenu>

          <ContextMenuDivider />
          <ContextMenuItem label="危险操作" value="danger" icon="⚠️" danger />
        </template>
      </ContextMenu>
    </div>

    <div class="demo-section">
      <h3>小尺寸菜单</h3>
      <div class="size-demo-container">
        <div class="size-demo-item">
          <h4>默认大小</h4>
          <ContextMenu @select="handleMenuSelect">
            <div class="demo-target demo-target-small">
              右键查看默认大小菜单
            </div>
            <template #content>
              <ContextMenuItem label="复制" value="copy-default" icon="📋" />
              <ContextMenuItem label="粘贴" value="paste-default" icon="📄" />
              <ContextMenuDivider />
              <ContextMenuSubmenu label="更多操作" icon="⚙️">
                <ContextMenuItem label="重命名" value="rename-default" icon="✏️" />
                <ContextMenuItem label="移动" value="move-default" icon="📁" />
              </ContextMenuSubmenu>
              <ContextMenuDivider />
              <ContextMenuItem label="删除" value="delete-default" icon="🗑️" danger />
            </template>
          </ContextMenu>
        </div>

        <div class="size-demo-item">
          <h4>小尺寸</h4>
          <ContextMenu @select="handleMenuSelect" size="small">
            <div class="demo-target demo-target-small">
              右键查看小尺寸菜单
            </div>
            <template #content>
              <ContextMenuItem label="复制" value="copy-small" icon="📋" />
              <ContextMenuItem label="粘贴" value="paste-small" icon="📄" />
              <ContextMenuDivider />
              <ContextMenuSubmenu label="更多操作" icon="⚙️">
                <ContextMenuItem label="重命名" value="rename-small" icon="✏️" />
                <ContextMenuItem label="移动" value="move-small" icon="📁" />
              </ContextMenuSubmenu>
              <ContextMenuDivider />
              <ContextMenuItem label="删除" value="delete-small" icon="🗑️" danger />
            </template>
          </ContextMenu>
        </div>
      </div>

      <div class="size-demo-container">
        <div class="size-demo-item">
          <h4>默认大小 - 分组菜单</h4>
          <ContextMenu @select="handleMenuSelect">
            <div class="demo-target demo-target-small">
              右键查看默认分组菜单
            </div>
            <template #content>
              <ContextMenuGroup title="编辑操作">
                <ContextMenuItem label="撤销" value="undo-default" icon="↩️" />
                <ContextMenuItem label="重做" value="redo-default" icon="↪️" />
              </ContextMenuGroup>
              <ContextMenuGroup title="文件操作">
                <ContextMenuItem label="新建" value="new-default" icon="➕" />
                <ContextMenuItem label="保存" value="save-default" icon="💾" />
              </ContextMenuGroup>
            </template>
          </ContextMenu>
        </div>

        <div class="size-demo-item">
          <h4>小尺寸 - 分组菜单</h4>
          <ContextMenu @select="handleMenuSelect" size="small">
            <div class="demo-target demo-target-small">
              右键查看小尺寸分组菜单
            </div>
            <template #content>
              <ContextMenuGroup title="编辑操作">
                <ContextMenuItem label="撤销" value="undo-small" icon="↩️" />
                <ContextMenuItem label="重做" value="redo-small" icon="↪️" />
              </ContextMenuGroup>
              <ContextMenuGroup title="文件操作">
                <ContextMenuItem label="新建" value="new-small" icon="➕" />
                <ContextMenuItem label="保存" value="save-small" icon="💾" />
              </ContextMenuGroup>
            </template>
          </ContextMenu>
        </div>
      </div>
    </div>

    <div v-if="selectedValue" class="result">
      <h3>选择结果：</h3>
      <p>{{ selectedValue }}</p>
    </div>
  </div>
</template>



<style lang="less">
.context-menu-demo {
  padding: 20px;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-target {
  width: 200px;
  height: 100px;
  border: 2px dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  user-select: none;
}

.demo-target:hover {
  border-color: #1890ff;
  background-color: #f6ffed;
}

.demo-target-small {
  width: 180px;
  height: 80px;
  font-size: 14px;
}

.size-demo-container {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.size-demo-item {
  flex: 1;
  min-width: 200px;
}

.size-demo-item h4 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.result {
  padding: 16px;
  background-color: #f0f8ff;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.result h3 {
  margin-top: 0;
  color: #1890ff;
}
</style>
